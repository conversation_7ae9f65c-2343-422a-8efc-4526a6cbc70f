# BSV Number Storage Project

This Flask application allows you to input a number and store it on the BSV blockchain using OP_RETURN. The transaction IDs are saved to a `transactions.json` file.

## Setup Instructions

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Configure BSV Settings
1. Copy `venv/config_example.py` to `venv/config.py`
2. Edit `venv/config.py` with your actual BSV credentials:
   - Get a BSV private key (WIF format)
   - Send some BSV to your address (minimum 0.001 BSV recommended)
   - Find a UTXO transaction ID and get the raw transaction hex
   - Update the configuration values

### 3. Run the Application
```bash
python venv/app.py
```

The application will be available at `http://localhost:5000`

## Features

- **Store Numbers**: Input a number (0-30) and store it on the BSV blockchain
- **Transaction Tracking**: All transaction IDs are saved to `venv/transactions.json`
- **View Transactions**: View all stored transactions and their data
- **Blockchain Links**: Direct links to view transactions on WhatsOnChain

## API Endpoints

- `GET /` - Main page
- `POST /store_number` - Store a number on the blockchain
- `GET /view_transactions` - View all stored transactions
- `GET /get_numbers` - Get all stored numbers
- `GET /update_transactions` - Update transaction data

## File Structure

- `venv/app.py` - Main Flask application
- `venv/templates/index.html` - Web interface
- `venv/transactions.json` - Stored transaction data
- `venv/config.py` - BSV configuration (create from config_example.py)
- `requirements.txt` - Python dependencies

## Notes

- Numbers must be between 0 and 30
- Each transaction costs a small fee (250 satoshis by default)
- Make sure you have sufficient BSV balance for transactions
- The application uses WhatsOnChain for broadcasting transactions

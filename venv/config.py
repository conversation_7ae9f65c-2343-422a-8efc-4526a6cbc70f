

# Your BSV private key in WIF (Wallet Import Format)
# Example: 'L1aW4aubDFB7yfras2S1mN3bqg9nwySY8nkoLmJebSLD5BWv3ENZ'
PRIVATE_KEY_WIF = 'L3PnxGwxCzTi984SCByJVbS4g1ir41bdcUoxz22DJ7A9LvgcdF3g'

# Wallet name for bitcoinlib (can be any string)
WALLET_NAME = 'bsv_write_number_wallet'

# Instructions:
# 1. Get a BSV private key in WIF format
# 2. Send some BSV to your address (minimum 0.001 BSV recommended)
# 3. Update PRIVATE_KEY_WIF above with your actual private key
# 4. Rename this file to config.py
#
# Note: The application will automatically create a wallet and manage UTXOs
# You just need to fund the wallet address with some BSV
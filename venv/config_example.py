# BSV Configuration Example
# Copy this file to config.py and fill in your actual values

# Your BSV private key (WIF format)
PRIVATE_KEY = 'your_private_key_here'

# Current UTXO to spend from
# You need to have a UTXO with some satoshis to create transactions
CURRENT_UTXO = {
    'txid': 'your_utxo_txid_here',  # Transaction ID of the UTXO
    'hex': 'your_utxo_hex_here',    # Raw transaction hex
    'output_index': 0               # Output index (usually 0)
}

# Instructions:
# 1. Get a BSV private key and address
# 2. Send some BSV to your address (minimum 0.001 BSV recommended)
# 3. Find the transaction ID and get the raw transaction hex
# 4. Update the values above
# 5. Rename this file to config.py

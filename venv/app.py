# This writes an inputted number to the blockchain and puts the txid in transactions.json
from flask import Flask, render_template, request, jsonify
import time
import asyncio
import json
from bsv import PrivateKey, P2PKH, Transaction, TransactionInput, TransactionOutput, OpReturn, WhatsOnChainBroadcaster

app = Flask(__name__)

# Try to import configuration
try:
    from config import PRIVATE_KEY, CURRENT_UTXO
except ImportError:
    # Fallback to example values
    PRIVATE_KEY = 'your_private_key_here'
    CURRENT_UTXO = {
        'txid': 'your_utxo_txid_here',
        'hex': 'your_utxo_hex_here',
        'output_index': 0
    }
    print("Warning: config.py not found. Using example values. Please create config.py from config_example.py")


def save_transaction_to_file(txid, number):
    """Save transaction ID and number to transactions.json"""
    try:
        # Read existing data
        try:
            with open('venv/transactions.json', 'r') as f:
                data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            data = {"transactions": []}

        # Add new transaction
        transaction_data = {
            "txid": txid,
            "number": number,
            "timestamp": time.time(),
            "op_return_data": f"number:{number}"
        }
        data["transactions"].append(transaction_data)

        # Save back to file
        with open('venv/transactions.json', 'w') as f:
            json.dump(data, f, indent=2)

        return True
    except Exception as e:
        print(f"Error saving transaction: {e}")
        return False

def update_transactions_file():
    """Update transactions.json with OP_RETURN data"""
    try:
        with open('venv/transactions.json', 'r') as f:
            data = json.load(f)

        with open('venv/transactions.json', 'w') as f:
            json.dump(data, f, indent=2)

        return data
    except Exception as e:
        print(f"Error updating transactions: {e}")
        return None

async def store_number_on_bsv(number):
    """Store a number on BSV blockchain"""
    try:
        priv_key = PrivateKey(PRIVATE_KEY)
        source_tx = Transaction.from_hex(CURRENT_UTXO['hex'])

        tx_input = TransactionInput(
            source_transaction=source_tx,
            source_txid=CURRENT_UTXO['txid'],
            source_output_index=CURRENT_UTXO['output_index'],
            unlocking_script_template=P2PKH().unlock(priv_key),
        )

        op_return_output = TransactionOutput(
            locking_script=OpReturn().lock([f"number:{number}"]),
            satoshis=0
        )

        change_output = TransactionOutput(
            locking_script=P2PKH().lock(priv_key.address()),
            change=True
        )

        tx = Transaction([tx_input], [change_output, op_return_output], version=1)
        tx.fee(250)
        tx.sign()

        broadcaster = WhatsOnChainBroadcaster()
        await broadcaster.broadcast(tx)
        
        return tx.txid()
    except Exception as e:
        print(f"Blockchain error: {e}")
        raise



@app.route('/')
def index():
    """Serve the main page"""
    return render_template('index.html')

@app.route('/store_number', methods=['POST'])
def store_number():
    try:
        number = float(request.json['number'])

        if not (0 < number < 30):
            return jsonify({'success': False, 'message': 'Number must be between 0 and 30'})

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        txid = loop.run_until_complete(store_number_on_bsv(number))
        loop.close()

        # Save transaction to file
        if save_transaction_to_file(txid, number):
            return jsonify({
                'success': True,
                'txid': txid,
                'number': number,
                'url': f'https://whatsonchain.com/tx/{txid}'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Transaction broadcast but failed to save to file'
            })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/update_transactions')
def update_transactions():
    """Update transactions.json with OP_RETURN data"""
    data = update_transactions_file()
    if data:
        return jsonify({'success': True, 'transactions': data['transactions']})
    else:
        return jsonify({'success': False, 'message': 'Failed to update transactions'})

@app.route('/view_transactions')
def view_transactions():
    """View current transactions data"""
    try:
        with open('venv/transactions.json', 'r') as f:
            data = json.load(f)
        return jsonify(data)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/get_numbers')
def get_numbers():
    """Get all numbers from stored transactions"""
    try:
        with open('venv/transactions.json', 'r') as f:
            data = json.load(f)
        numbers = [tx['number'] for tx in data.get('transactions', [])]
        return jsonify({'numbers': numbers})
    except Exception as e:
        return jsonify({'error': str(e), 'numbers': []})



if __name__ == '__main__':
    app.run(debug=True)

# python3 venv/app.py
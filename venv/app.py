# This writes an inputted number to the blockchain and puts the txid in transactions.json
from flask import Flask, render_template, request, jsonify
import time
import json
import requests
from bitcoinlib.wallets import Wallet
from bitcoinlib.keys import Key

app = Flask(__name__)

# Try to import configuration
try:
    from config import PRIVATE_KEY_WIF, WALLET_NAME
except ImportError:
    # Fallback to example values
    PRIVATE_KEY_WIF = 'your_private_key_wif_here'
    WALLET_NAME = 'bsv_input_number_wallet'
    print("Warning: config.py not found. Using example values. Please create config.py from config_example.py")


def save_transaction_to_file(txid, number):
    """Save transaction ID and number to transactions.json"""
    try:
        # Read existing data
        try:
            with open('venv/transactions.json', 'r') as f:
                data = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            data = {"transactions": []}

        # Add new transaction
        transaction_data = {
            "txid": txid,
            "number": number,
            "timestamp": time.time(),
            "op_return_data": f"number:{number}"
        }
        data["transactions"].append(transaction_data)

        # Save back to file
        with open('venv/transactions.json', 'w') as f:
            json.dump(data, f, indent=2)

        return True
    except Exception as e:
        print(f"Error saving transaction: {e}")
        return False

def update_transactions_file():
    """Update transactions.json with OP_RETURN data"""
    try:
        with open('venv/transactions.json', 'r') as f:
            data = json.load(f)

        with open('venv/transactions.json', 'w') as f:
            json.dump(data, f, indent=2)

        return data
    except Exception as e:
        print(f"Error updating transactions: {e}")
        return None

def get_or_create_wallet():
    """Get or create a BSV wallet"""
    try:
        # Try to open existing wallet
        wallet = Wallet(WALLET_NAME, network='bitcoinsv')
        return wallet
    except:
        # Create new wallet if it doesn't exist
        if PRIVATE_KEY_WIF != 'your_private_key_wif_here':
            # Create wallet from private key
            key = Key(PRIVATE_KEY_WIF, network='bitcoinsv')
            wallet = Wallet.create(WALLET_NAME, keys=key, network='bitcoinsv')
        else:
            # Create new wallet with random key
            wallet = Wallet.create(WALLET_NAME, network='bitcoinsv')
        return wallet

def store_number_on_bsv(number):
    """Store a number on BSV blockchain using OP_RETURN"""
    try:
        wallet = get_or_create_wallet()

        # Check wallet balance
        wallet.scan()
        balance = wallet.balance()

        if balance < 1000:  # Need at least 1000 satoshis for transaction
            raise Exception(f"Insufficient balance: {balance} satoshis. Need at least 1000 satoshis.")

        # Create OP_RETURN data
        op_return_data = f"number:{number}".encode('utf-8')

        # Create transaction with OP_RETURN output
        tx = wallet.send_to([], fee=250, op_return_data=op_return_data)

        if tx:
            return tx.txid
        else:
            raise Exception("Failed to create transaction")

    except Exception as e:
        print(f"Blockchain error: {e}")
        raise



@app.route('/')
def index():
    """Serve the main page"""
    return render_template('index.html')

@app.route('/store_number', methods=['POST'])
def store_number():
    try:
        number = float(request.json['number'])

        if not (0 < number < 30):
            return jsonify({'success': False, 'message': 'Number must be between 0 and 30'})

        # Store number on BSV blockchain
        txid = store_number_on_bsv(number)

        # Save transaction to file
        if save_transaction_to_file(txid, number):
            return jsonify({
                'success': True,
                'txid': txid,
                'number': number,
                'url': f'https://whatsonchain.com/tx/{txid}'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Transaction broadcast but failed to save to file'
            })
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/update_transactions')
def update_transactions():
    """Update transactions.json with OP_RETURN data"""
    data = update_transactions_file()
    if data:
        return jsonify({'success': True, 'transactions': data['transactions']})
    else:
        return jsonify({'success': False, 'message': 'Failed to update transactions'})

@app.route('/view_transactions')
def view_transactions():
    """View current transactions data"""
    try:
        with open('venv/transactions.json', 'r') as f:
            data = json.load(f)
        return jsonify(data)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/get_numbers')
def get_numbers():
    """Get all numbers from stored transactions"""
    try:
        with open('venv/transactions.json', 'r') as f:
            data = json.load(f)
        numbers = [tx['number'] for tx in data.get('transactions', [])]
        return jsonify({'numbers': numbers})
    except Exception as e:
        return jsonify({'error': str(e), 'numbers': []})

@app.route('/wallet_info')
def wallet_info():
    """Get wallet information"""
    try:
        wallet = get_or_create_wallet()
        wallet.scan()

        return jsonify({
            'address': wallet.get_key().address,
            'balance': wallet.balance(),
            'network': 'BSV',
            'wallet_name': WALLET_NAME
        })
    except Exception as e:
        return jsonify({'error': str(e)})



if __name__ == '__main__':
    app.run(debug=True, port=5001)

# python3 venv/app.py